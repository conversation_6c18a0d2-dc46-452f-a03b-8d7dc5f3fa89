/*
 * Copyright 2022 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.internal.cc.impl


import org.gradle.test.fixtures.file.TestFile
import org.gradle.test.fixtures.server.http.HttpServer
import org.junit.Rule
import spock.lang.Issue

class ConfigurationCacheRemoteBuildScriptIntegrationTest extends AbstractConfigurationCacheIntegrationTest {

    @Rule
    HttpServer server = new HttpServer()

    String scriptUrl
    TestFile scriptFile
    String scriptName = "remote-script.gradle"
    def configurationCache

    def setup() {
        server.start()

        scriptUrl = "${server.uri}/${scriptName}"
        scriptFile = file("remote-script.gradle") << """
            println 'loaded remote script'
        """
        server.expectGet "/$scriptName", scriptFile

        buildFile << """
            apply from: '$scriptUrl'
            task ok
        """

        configurationCache = newConfigurationCacheFixture()
    }

    @Issue("https://github.com/gradle/gradle/issues/23273")
    def "invalidates cache if remote script was changed"() {
        when:
        configurationCacheRun 'ok'

        then:
        configurationCache.assertStateStored()

        when:
        scriptFile << """
            print 'update remote script'
        """
        server.expectHead "/$scriptName", scriptFile
        server.expectGet "/$scriptName", scriptFile

        and:
        configurationCacheRun 'ok'

        then:
        output.contains "Calculating task graph as configuration cache cannot be reused because remote script $scriptUrl has changed."
        configurationCache.assertStateStored()
    }

    def "reuse cache if remote script is up to date"() {
        when:
        configurationCacheRun 'ok'

        then:
        configurationCache.assertStateStored()

        and:
        server.expectHead "/$scriptName", scriptFile

        when:
        configurationCacheRun 'ok'

        then:
        configurationCache.assertStateLoaded()
    }

    def "reuse cache for offline build"() {
        when:
        configurationCacheRun 'ok'

        then:
        configurationCache.assertStateStored()

        when:
        scriptFile << """
            print 'update remote script'
        """

        and:
        executer.withArgument("--offline")
        configurationCacheRun 'ok'

        then:
        configurationCache.assertStateStored()

        and:
        outputDoesNotContain 'update remote script'

        when:
        executer.withArgument("--offline")
        configurationCacheRun 'ok'

        then:
        configurationCache.assertStateLoaded()
    }
}
