/*
 * Copyright 2022 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.internal.cc.impl.inputs.process.instrument

/**
 * Test cases for single-argument {@code ProcessGroovyMethods.execute}:
 * <pre>
 *     ProcessGroovyMethod.execute("echo 123")
 *     ProcessGroovyMethod.execute(["echo", "123"])
 * </pre>
 */
class Execute1QualifiedStaticInstrumentationInDynamicGroovyWithIndyIntegrationTest extends AbstractProcessInstrumentationInDynamicGroovyIntegrationTest {
    @Override
    def indyModes() {
        return [true]
    }

    @Override
    def testCases() {
        return [
            // Direct ProcessGroovyMethods calls
            // varInitializer | processCreator | expectedPwdSuffix | expectedEnvVar
            [fromString(), "ProcessGroovyMethods.execute(command)", "", ""],
            [fromGroovyString(), "ProcessGroovyMethods.execute(command)", "", ""],
            [fromStringArray(), "ProcessGroovyMethods.execute(command)", "", ""],
            [fromStringList(), "ProcessGroovyMethods.execute(command)", "", ""],
            [fromObjectList(), "ProcessGroovyMethods.execute(command)", "", ""],
            // Spread calls
            [fromString(), "ProcessGroovyMethods.execute(*[command])", "", ""],
            // type-wrapped arguments
            [fromGroovyString(), "ProcessGroovyMethods.execute(command as String)", "", ""],
        ]
    }
}
