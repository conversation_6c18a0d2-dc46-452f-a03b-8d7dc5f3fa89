/*
 * Copyright 2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.internal.cc.impl.serialization.codecs

import org.junit.Test


/**
 * [Lazy] values support Java serialization via a custom `writeReplace` method that forces their evaluation.
 */
class LazyCodecTest : AbstractFunctionalTypeTest() {

    @Test
    fun `forces evaluation of Lazy objects`() {
        assertEagerEvaluationOf(lazy()) {
            value
        }
    }

    @Test
    fun `forces evaluation of dynamic Lazy fields`() {
        assertEagerEvaluationOf(BeanOf(lazy())) {
            value.value
        }
    }

    @Test
    fun `forces evaluation of static Lazy fields`() {
        assertEagerEvaluationOf(LazyBean(lazy())) {
            value.value
        }
    }

    @Test
    fun `forces evaluation of by lazy properties`() {
        assertEagerEvaluationOf(ByLazyBean()) {
            value
        }
    }

    private
    fun lazy(): Lazy<Any?> = lazy { Runtime.value }

    data class LazyBean(val value: Lazy<Any?>)

    class ByLazyBean {
        val value by lazy { Runtime.value }
    }
}
